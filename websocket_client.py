import asyncio
import websockets

async def test_client():
    """测试WebSocket客户端"""
    uri = "ws://localhost:8766"
    
    try:
        async with websockets.connect(uri) as websocket:
            print(f"已连接到服务器: {uri}")
            
            # 发送测试消息
            test_messages = [
                "你好，服务器！",
                "这是第二条消息",
                "测试中文字符：你好世界！"
            ]
            
            for message in test_messages:
                print(f"发送消息: {message}")
                await websocket.send(message)
                
                # 接收服务器回复
                response = await websocket.recv()
                print(f"收到回复: {response}")
                
                # 等待一秒
                await asyncio.sleep(1)
            
            # 等待接收定时消息
            print("等待接收服务器定时消息...")
            try:
                for i in range(3):  # 接收3条定时消息
                    message = await asyncio.wait_for(websocket.recv(), timeout=6)
                    print(f"收到定时消息: {message}")
            except asyncio.TimeoutError:
                print("等待定时消息超时")
                
    except ConnectionRefusedError:
        print("连接被拒绝，请确保服务器正在运行")
    except Exception as e:
        print(f"客户端错误: {e}")

if __name__ == "__main__":
    asyncio.run(test_client())
