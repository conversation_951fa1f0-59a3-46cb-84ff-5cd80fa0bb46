import asyncio
import websockets

# 存储所有连接的客户端
connected_clients = set()

async def handle_connection(websocket):
    """处理WebSocket连接的函数 - 兼容新版本websockets库"""
    connected_clients.add(websocket)
    print(f"新客户端连接，当前连接数: {len(connected_clients)}")

    try:
        async for message in websocket:
            print(f"收到客户端消息: {message}")
            response = f"服务端已收到: {message}"
            await websocket.send(response)
            print(f"已发送回复: {response}")
    except websockets.exceptions.ConnectionClosed:
        print("客户端连接关闭")
    except Exception as e:
        print(f"连接处理出错: {e}")
    finally:
        if websocket in connected_clients:
            connected_clients.remove(websocket)
        print(f"客户端断开，当前连接数: {len(connected_clients)}")

async def send_periodic_messages():
    while True:
        await asyncio.sleep(5)
        if connected_clients:
            message = "这是服务端的定时消息"
            print(f"向所有客户端发送: {message}")
            for client in connected_clients.copy():
                try:
                    await client.send(message)
                except Exception as e:
                    print(f"发送失败: {e}")

async def main():
    try:
        server = await serve(
            handle_connection,
            "0.0.0.0",
            8765
        )
        print("WebSocket服务器已启动，监听 ws://localhost:8765")

        # 启动定时消息任务
        asyncio.create_task(send_periodic_messages())

        # 保持服务器运行
        await server.wait_closed()
    except Exception as e:
        print(f"服务器启动失败: {e}")
        raise

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("服务器已关闭")